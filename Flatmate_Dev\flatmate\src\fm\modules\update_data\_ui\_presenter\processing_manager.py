"""
Processing management for Update Data module.

This module handles all file processing logic including:
- Process initiation
- Processing event handling
- Processing status updates
- Unrecognized file handling

Extracted from the monolithic UpdateDataPresenter as part of the decomposition refactoring.
"""

from typing import TYPE_CHECKING

from ....core.services.logger import log
from ..pipeline.dw_director import dw_director
from ..services.local_event_bus import ViewEvents
from .._ui.events_data import (
    ProcessingStartedEvent,
    ProcessingCompletedEvent,
    DialogRequestEvent
)

if TYPE_CHECKING:
    from ..interface import IUpdateDataView


class ProcessingManager:
    """
    Manages all file processing operations.

    This class handles:
    - Process button click handling
    - Job sheet creation and validation
    - File processing execution via dw_director
    - Processing event handling (started, stats, completed)
    - Error handling and user feedback
    """

    def __init__(self, view: 'IUpdateDataView', state, info_bar_service, local_bus):
        """
        Initialize the processing manager.

        Args:
            view: The view interface
            state: The presenter state object
            info_bar_service: Service for info bar messages
            local_bus: Local event bus for communication
        """
        self.view = view
        self.state = state
        self.info_bar_service = info_bar_service
        self.local_bus = local_bus

        # Legacy state tracking (will be migrated to state manager)
        self.selected_source = None
        self.save_location = None
        self.job_sheet_dict = None

    def set_source_and_destination(self, selected_source, save_location):
        """
        Set the source and destination for processing.

        Args:
            selected_source: Dictionary containing source information
            save_location: Path to save location
        """
        self.selected_source = selected_source
        self.save_location = save_location

    def get_files_for_processing(self):
        """Get the current list of files for processing."""
        try:
            # Get files from file_pane (source of truth)
            if hasattr(self.view, 'file_pane'):
                current_files = self.view.file_pane.get_files()
                log.debug(f"[PROCESSING_MANAGER] Retrieved {len(current_files)} files from file_pane")
                return current_files
            # Fallback to state if file_pane not accessible
            else:
                log.debug("[PROCESSING_MANAGER] Using state.selected_files as fallback")
                return self.state.selected_files or []
                
        except Exception as e:
            log.error(f"Error getting files for processing: {e}")
            return []
    
    def handle_process(self):
        """
        Handle process button click.

        MIGRATION: Now emits events instead of direct state coordinator calls.
        """
        # Get files directly from file_pane (source of truth)
        files_to_process = self.get_files_for_processing()
        destination = self.state.save_location
        
        log.debug(f"[PROCESSING_MANAGER] Processing {len(files_to_process)} files")

        if not files_to_process:
            # MIGRATION: Emit error event instead of direct view call
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              DialogRequestEvent(
                                  dialog_type="error",
                                  title="Error",
                                  extra_data={"message": "No source files selected."}
                              ))
            return

        if not self.save_location:
            # MIGRATION: Emit error event instead of direct view call
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              DialogRequestEvent(
                                  dialog_type="error",
                                  title="Error",
                                  extra_data={"message": "No save location selected."}
                              ))
            return

        # Create job sheet
        self.job_sheet_dict = {
            "filepaths": files_to_process,
            "save_folder": self.save_location,
            "update_database": self.view.get_update_database(),
        }

        # MIGRATION: Emit processing started event instead of direct state coordinator call
        processing_data = ProcessingStartedEvent(job_sheet=self.job_sheet_dict)
        self.local_bus.emit(ViewEvents.PROCESSING_STARTED.value, processing_data)

        # Log the job sheet for debugging
        log.debug(f"Processing job sheet: {self.job_sheet_dict}")

        try:
            # Use the director function to process the files
            result = dw_director(self.job_sheet_dict)

            # Check the result from the director
            if result.get("status") == "success":
                # MIGRATION: Emit processing completed event instead of direct calls
                processed_count = len(self.job_sheet_dict.get("filepaths", []))
                completion_data = ProcessingCompletedEvent(
                    success=True,
                    result={
                        "processed_count": processed_count,
                        "message": result.get("message", "Processing complete!")
                    }
                )
                self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, completion_data)

                # MIGRATION: Emit success dialog event instead of direct view call
                self.local_bus.emit(ViewEvents.SUCCESS_DIALOG_REQUESTED.value,
                                  DialogRequestEvent(
                                      dialog_type="success",
                                      title="Success",
                                      extra_data={"message": result.get("message", "Processing complete!")}
                                  ))
            else:
                error_msg = result.get("message", "An unknown error occurred.")
                log.error(f"Processing error from director: {result}")

                # MIGRATION: Emit processing failed and error dialog events
                failure_data = ProcessingCompletedEvent(
                    success=False,
                    result={"error": error_msg}
                )
                self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, failure_data)
                self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                                  DialogRequestEvent(
                                      dialog_type="error",
                                      title="Error",
                                      extra_data={"message": error_msg}
                                  ))

        except Exception as e:
            log.error(f"Error during processing: {e}")

            # MIGRATION: Emit processing failed and error dialog events
            failure_data = ProcessingCompletedEvent(
                success=False,
                result={"error": str(e)}
            )
            self.local_bus.emit(ViewEvents.PROCESSING_COMPLETED.value, failure_data)
            self.local_bus.emit(ViewEvents.ERROR_DIALOG_REQUESTED.value,
                              DialogRequestEvent(
                                  dialog_type="error",
                                  title="Error",
                                  extra_data={"message": f"An error occurred: {e}"}
                              ))

        # MIGRATION: Reset state via events instead of direct view calls
        self.selected_source = None
        # TODO: Add reset/cleanup events if needed
        # For now, the state coordinator will handle UI reset via processing completion events

    def on_processing_started(self, job_sheet):
        """Handle processing started event."""
        file_count = len(job_sheet.get("filepaths", [])) or 0
        log.info(f"Processing started for {file_count} files")

        # Update both the InfoBar and the info widget for consistency
        self.info_bar_service.show()
        self.info_bar_service.publish_message(f"Processing {file_count} files...")

    def on_processing_stats(self, stats):
        """Handle processing stats event."""
        log.debug(f"Processing stats: {stats}")
        total = stats.get("total_files", 0)
        processed = stats.get("processed_files", 0)
        unrecognized = stats.get("unrecognized_files", 0)

        # Update progress in the info widget
        self.info_bar_service.publish_message(f"Processing: {processed}/{total} files")

        # Update the InfoBar with current progress
        if total > 0:
            self.info_bar_service.publish_message(
                f"Processing files: {processed}/{total} complete"
            )

    def on_unrecognized_files(self, unrecognized_files):
        """Handle unrecognized files event."""
        log.warning(f"Unrecognized files detected: {unrecognized_files}")

        # Update the InfoBar with a warning about unrecognized files
        count = len(unrecognized_files)
        self.info_bar_service.publish_message(
            f"Warning: {count} unrecognized file(s) detected"
        )

        # Also update the info widget with detailed error messages
        for file_info in unrecognized_files:
            self.info_bar_service.publish_message(
                f"Warning: Unrecognized file: {file_info['filepath']}\n"
                f"Reason: {file_info.get('reason', 'Unknown')}"
            )

    def on_processing_completed(self, result):
        """Handle processing completed event.

        Args:
            result: ProcessingCompletedEvent dataclass instance
        """
        log.info("File processing completed")

        # Handle both dict (old format) and dataclass (new format)
        if hasattr(result, 'get'):
            # Dictionary format
            stats = result.get("stats", {})
            processed = stats.get("processed_files", 0)
            unrecognized = stats.get("unrecognized_files", 0)
            backup_stats = result.get("backup_stats", {})
            backed_up = backup_stats.get("backed_up_count", 0)
            skipped = backup_stats.get("skipped_count", 0)
        else:
            # Dataclass format
            if hasattr(result, 'result') and result.result:
                stats = result.result.get("stats", {})
                processed = stats.get("processed_files", 0)
                unrecognized = stats.get("unrecognized_files", 0)
                backup_stats = result.result.get("backup_stats", {})
                backed_up = backup_stats.get("backed_up_count", 0)
                skipped = backup_stats.get("skipped_count", 0)
            else:
                # Default values if no result data
                processed = 0
                unrecognized = 0
                backed_up = 0
                skipped = 0

        # Update the InfoBar with completion message
        status_msg = f"Processing complete. {processed} files processed successfully."
        self.info_bar_service.publish_message(status_msg)

        # Build status message with all stats
        status_msg = f"Processing complete. {processed} files processed successfully."

        # Add backup stats if available
        if backed_up > 0 or skipped > 0:
            status_msg += (
                f" {backed_up} files backed up, {skipped} identical files skipped."
            )

        # Add error info if unrecognized files exist
        if unrecognized > 0:
            status_msg += f" {unrecognized} files unrecognized."
            self.info_bar_service.publish_message(f"Error: {status_msg}")
        else:
            self.info_bar_service.publish_message(status_msg)
